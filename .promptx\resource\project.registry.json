{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-29T17:32:17.238Z", "updatedAt": "2025-07-29T17:32:17.241Z", "resourceCount": 3}, "resources": [{"id": "langgraph-workflow", "source": "project", "protocol": "execution", "name": "Langgraph Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/langgraph-expert/execution/langgraph-workflow.execution.md", "metadata": {"createdAt": "2025-07-29T17:32:17.240Z", "updatedAt": "2025-07-29T17:32:17.240Z", "scannedAt": "2025-07-29T17:32:17.240Z", "path": "role/langgraph-expert/execution/langgraph-workflow.execution.md"}}, {"id": "langgraph-expert", "source": "project", "protocol": "role", "name": "Langgraph Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/langgraph-expert/langgraph-expert.role.md", "metadata": {"createdAt": "2025-07-29T17:32:17.240Z", "updatedAt": "2025-07-29T17:32:17.240Z", "scannedAt": "2025-07-29T17:32:17.240Z", "path": "role/langgraph-expert/langgraph-expert.role.md"}}, {"id": "multi-agent-thinking", "source": "project", "protocol": "thought", "name": "Multi Agent Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/langgraph-expert/thought/multi-agent-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T17:32:17.241Z", "updatedAt": "2025-07-29T17:32:17.241Z", "scannedAt": "2025-07-29T17:32:17.241Z", "path": "role/langgraph-expert/thought/multi-agent-thinking.thought.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "role": 1, "thought": 1}, "bySource": {"project": 3}}}