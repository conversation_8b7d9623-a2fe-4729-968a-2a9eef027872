<execution>
  <constraint>
    ## LangGraph技术约束
    - **Python环境依赖**：需要Python 3.8+和兼容的LangChain版本
    - **状态序列化限制**：状态对象必须可JSON序列化或使用pickle
    - **内存管理约束**：大状态对象需要考虑内存使用和垃圾回收
    - **并发执行限制**：同一时间只有一个智能体可以修改状态
    - **检查点大小限制**：持久化状态不应超过合理的存储限制
  </constraint>

  <rule>
    ## LangGraph开发强制规则
    - **状态定义优先**：必须先用TypedDict定义清晰的状态结构
    - **智能体函数规范**：每个智能体函数必须接受state参数并返回状态更新
    - **边界条件检查**：每个智能体必须验证输入状态的有效性
    - **错误处理强制**：关键智能体必须包含try-catch错误处理
    - **日志记录要求**：每个状态转换必须记录调试信息
    - **测试覆盖率**：每个智能体函数必须有对应的单元测试
  </rule>

  <guideline>
    ## 开发指导原则
    - **渐进式构建**：从简单的线性流程开始，逐步增加复杂性
    - **模块化设计**：智能体函数应该独立、可复用、易测试
    - **可视化优先**：使用图形化工具展示智能体协作流程
    - **性能意识**：考虑智能体执行的时间复杂度和资源消耗
    - **用户体验**：提供清晰的执行状态反馈和进度指示
    - **代码可读性**：使用清晰的命名和充分的注释说明
  </guideline>

  <process>
    ## LangGraph多智能体开发流程
    
    ### Step 1: 项目初始化与依赖管理
    ```python
    # 标准项目结构
    project/
    ├── agents/          # 智能体实现
    ├── states/          # 状态定义
    ├── graphs/          # 图构建逻辑
    ├── utils/           # 工具函数
    └── tests/           # 测试代码
    ```
    
    ### Step 2: 状态结构设计
    ```python
    from typing_extensions import TypedDict
    from typing import List, Dict, Any
    
    class AgentState(TypedDict):
        messages: List[Dict[str, Any]]
        current_step: str
        context: Dict[str, Any]
        history: List[Dict[str, Any]]
    ```
    
    ### Step 3: 智能体函数实现
    ```python
    def researcher_agent(state: AgentState) -> AgentState:
        # 智能体逻辑实现
        return {"messages": [...], "current_step": "analysis"}
    ```
    
    ### Step 4: 图构建与路由
    ```python
    from langgraph.graph import StateGraph
    
    workflow = StateGraph(AgentState)
    workflow.add_node("researcher", researcher_agent)
    workflow.add_node("analyzer", analyzer_agent)
    workflow.add_conditional_edges(
        "researcher",
        router_function,
        {"continue": "analyzer", "end": END}
    )
    ```
    
    ### Step 5: 执行与监控
    ```mermaid
    flowchart TD
        A[初始化状态] --> B[执行图]
        B --> C{检查结果}
        C -->|成功| D[状态更新]
        C -->|失败| E[错误处理]
        D --> F[继续执行]
        E --> G[重试或降级]
        F --> H[完成]
        G --> B
    ```
    
    ### Step 6: 调试与优化
    - **可视化调试**：使用LangGraph的内置可视化工具
    - **状态检查点**：在关键节点设置状态快照
    - **性能分析**：监控每个智能体的执行时间
    - **错误追踪**：完整的错误堆栈和状态历史
  </process>

  <criteria>
    ## 质量评估标准
    
    ### 功能性评估
    - ✅ 智能体功能符合预期
    - ✅ 状态转换逻辑正确  
    - ✅ 错误处理机制完善
    - ✅ 边界条件处理恰当
    
    ### 性能评估
    - ✅ 执行时间在可接受范围内
    - ✅ 内存使用合理高效
    - ✅ 并发处理能力满足需求
    - ✅ 扩展性良好
    
    ### 可维护性评估
    - ✅ 代码结构清晰规范
    - ✅ 文档完整准确
    - ✅ 测试覆盖率充分
    - ✅ 调试信息丰富
    
    ### 用户体验评估
    - ✅ 执行过程可观测
    - ✅ 错误信息友好
    - ✅ 性能表现稳定
    - ✅ 配置使用简单
  </criteria>
</execution>